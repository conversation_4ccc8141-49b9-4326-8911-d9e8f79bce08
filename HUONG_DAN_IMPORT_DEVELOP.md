# HƯỚNG DẪN IMPORT DỮ LIỆU LÊN MÔI TRƯỜNG DEVELOP

## 📋 THÔNG TIN TASK
- **Jira Task:** UBG-2824
- **Epic:** UBG-2790  
- **<PERSON><PERSON><PERSON> tiêu:** Import dữ liệu hành chính VN từ localhost lên develop
- **Ngày tạo:** 2025-07-15

## 🎯 TỔNG QUAN

Chuyển dữ liệu đã crawl thành công từ localhost lên môi trường develop để tiếp tục phát triển:
- **34 tỉnh thành** (bảng `tinhthanh`)
- **3,321 xã phường** (bảng `xaphuong`) 
- **3,312 geometry data** (bảng `ward_geometry`)

## 🚀 BƯỚC 1: CHUẨN BỊ TRÊN LOCALHOST

### 1.1 Chạy script export
```bash
# Cấp quyền thực thi
chmod +x backup_and_export.sh

# Chạy script export
./backup_and_export.sh
```

### 1.2 Ki<PERSON>m tra file đã tạo
```bash
ls -la exports/
```

Sẽ có các file:
- `urbox_admin_complete_YYYYMMDD_HHMMSS.sql` (file chính để import)
- `urbox_full_backup_YYYYMMDD_HHMMSS.sql` (backup toàn bộ)
- `tinhthanh_data_YYYYMMDD_HHMMSS.sql`
- `xaphuong_data_YYYYMMDD_HHMMSS.sql`
- `ward_geometry_data_YYYYMMDD_HHMMSS.sql`
- `admin_schema_YYYYMMDD_HHMMSS.sql`

## 🌐 BƯỚC 2: UPLOAD LÊN SERVER DEVELOP

### 2.1 Upload file chính
```bash
# Sử dụng scp hoặc rsync
scp exports/urbox_admin_complete_*.sql user@develop-server:/path/to/upload/

# Hoặc sử dụng FTP/SFTP client
```

### 2.2 Kiểm tra file trên server
```bash
# SSH vào server develop
ssh user@develop-server

# Kiểm tra file đã upload
ls -la /path/to/upload/urbox_admin_complete_*.sql
```

## 🗄️ BƯỚC 3: CHUẨN BỊ DATABASE DEVELOP

### 3.1 Backup database hiện tại (quan trọng!)
```bash
# Backup toàn bộ database urbox trước khi import
mysqldump -u username -p --single-transaction urbox > urbox_backup_before_import_$(date +%Y%m%d_%H%M%S).sql
```

### 3.2 Kiểm tra kết nối database
```bash
mysql -u username -p -e "USE urbox; SHOW TABLES;"
```

## 📥 BƯỚC 4: THỰC HIỆN IMPORT

### 4.1 Import dữ liệu
```bash
# Import file chính
mysql -u username -p urbox < urbox_admin_complete_*.sql
```

### 4.2 Kiểm tra quá trình import
Trong quá trình import, script sẽ hiển thị:
- Tạo cấu trúc bảng
- Import dữ liệu từng bảng
- Kiểm tra số lượng records
- Thông báo hoàn thành

## ✅ BƯỚC 5: KIỂM TRA VÀ VERIFY

### 5.1 Kiểm tra số lượng dữ liệu
```sql
USE urbox;

-- Kiểm tra số lượng records
SELECT 
    'tinhthanh' as table_name, 
    COUNT(*) as record_count,
    '34' as expected_count
FROM tinhthanh
UNION ALL
SELECT 
    'xaphuong' as table_name, 
    COUNT(*) as record_count,
    '3321' as expected_count
FROM xaphuong
UNION ALL
SELECT 
    'ward_geometry' as table_name, 
    COUNT(*) as record_count,
    '3312' as expected_count
FROM ward_geometry;
```

### 5.2 Kiểm tra dữ liệu mẫu
```sql
-- Kiểm tra tỉnh thành
SELECT mahc, tentinh FROM tinhthanh ORDER BY mahc LIMIT 5;

-- Kiểm tra xã phường
SELECT id, matinh, ma, tentinh, loai, tenhc 
FROM xaphuong 
WHERE matinh = 1 
ORDER BY tenhc 
LIMIT 5;

-- Kiểm tra geometry
SELECT id, pti_id, 
       CASE 
           WHEN data IS NOT NULL THEN 'Có geometry data'
           ELSE 'Không có geometry data'
       END as geometry_status
FROM ward_geometry 
ORDER BY id 
LIMIT 5;
```

### 5.3 Kiểm tra tính toàn vẹn dữ liệu
```sql
-- Kiểm tra join giữa các bảng
SELECT 
    t.mahc,
    t.tentinh,
    COUNT(x.id) as ward_count
FROM tinhthanh t
LEFT JOIN xaphuong x ON x.matinh = t.mahc
GROUP BY t.mahc, t.tentinh
ORDER BY t.mahc
LIMIT 10;
```

### 5.4 Kiểm tra charset tiếng Việt
```sql
-- Kiểm tra hiển thị tiếng Việt
SELECT tentinh FROM tinhthanh WHERE mahc = 1;
SELECT tenhc FROM xaphuong WHERE matinh = 1 LIMIT 3;
```

## 🚨 XỬ LÝ LỖI

### Lỗi charset
```sql
-- Nếu tiếng Việt bị lỗi, chạy:
ALTER TABLE tinhthanh CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE xaphuong CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### Lỗi quyền truy cập
```bash
# Cấp quyền cho user
GRANT ALL PRIVILEGES ON urbox.* TO 'username'@'%';
FLUSH PRIVILEGES;
```

### Lỗi dung lượng
```sql
-- Kiểm tra dung lượng database
SELECT 
    table_schema as 'Database',
    table_name as 'Table',
    round(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'urbox'
ORDER BY (data_length + index_length) DESC;
```

## 📊 KẾT QUẢ MONG ĐỢI

Sau khi import thành công:
- ✅ Database urbox có 3 bảng mới: `tinhthanh`, `xaphuong`, `ward_geometry`
- ✅ Tổng cộng 6,667 records (34 + 3,321 + 3,312)
- ✅ Dữ liệu tiếng Việt hiển thị chính xác
- ✅ Có thể query và join giữa các bảng
- ✅ Sẵn sàng cho các task tiếp theo: migration và API development

## 🔄 BƯỚC TIẾP THEO

1. **Task 5:** Cập nhật bảng `province` với `is_merge=2`
2. **Task 6:** Cập nhật bảng `ward` 
3. **Task 15-16:** Phát triển API mới cho tỉnh/xã
4. **Task 20:** API cho mobile app

## 📞 HỖ TRỢ

Nếu gặp vấn đề:
1. Kiểm tra log import
2. Verify backup đã tạo
3. Liên hệ team để hỗ trợ
4. Cập nhật tiến độ vào Jira UBG-2824
