#!/bin/bash

# =====================================================
# SCRIPT EXPORT DỮ LIỆU BẢNG ___PROVINCE VÀ WARD
# =====================================================
# Dự án: Import 34 tỉnh thành và 3,321 xã phường
# Tạo: 2025-07-15
# Mục đích: Export dữ liệu từ localhost để import vào develop

# Cấu hình database
DB_HOST="localhost"
DB_USER="root"
DB_PASS="root"
DB_NAME="urbox"
EXPORT_DIR="./exports"
DATE=$(date +"%Y%m%d_%H%M%S")

# Tạo thư mục export nếu chưa có
mkdir -p $EXPORT_DIR

echo "🚀 Bắt đầu export dữ liệu bảng ___province và ward..."
echo "📅 Thời gian: $(date)"
echo "📁 Thư mục export: $EXPORT_DIR"

# =====================================================
# PHẦN 1: KIỂM TRA DỮ LIỆU TRƯỚC KHI EXPORT
# =====================================================

echo ""
echo "🔍 KIỂM TRA DỮ LIỆU HIỆN TẠI..."

# Kiểm tra số lượng province
PROVINCE_NEW=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM ___province WHERE is_megre = 2")
PROVINCE_OLD=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM ___province WHERE is_megre = 1")

echo "📊 Bảng ___province:"
echo "   - Records mới (is_megre = 2): $PROVINCE_NEW"
echo "   - Records cũ (is_megre = 1): $PROVINCE_OLD"

# Kiểm tra số lượng ward (chỉ lấy ward có province_id khớp với ___province mới)
WARD_NEW=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM ward w INNER JOIN ___province p ON w.province_id = p.id WHERE w.is_merge = 2 AND p.is_megre = 2")
WARD_OLD=$(mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "SELECT COUNT(*) FROM ward WHERE is_merge = 1")

echo "📊 Bảng ward:"
echo "   - Records mới (is_merge = 2): $WARD_NEW"
echo "   - Records cũ (is_merge = 1): $WARD_OLD"

# Kiểm tra số liệu mong đợi
if [ "$PROVINCE_NEW" != "34" ]; then
    echo "⚠️ CẢNH BÁO: Số lượng province mới ($PROVINCE_NEW) khác 34!"
fi

if [ "$WARD_NEW" != "3321" ]; then
    echo "⚠️ CẢNH BÁO: Số lượng ward mới ($WARD_NEW) khác 3,321!"
fi

# =====================================================
# PHẦN 2: EXPORT BẢNG ___PROVINCE
# =====================================================

echo ""
echo "📤 EXPORT BẢNG ___PROVINCE..."

PROVINCE_FILE="$EXPORT_DIR/province_data_$DATE.sql"

# Tạo header cho file
cat > $PROVINCE_FILE << 'EOF'
-- =====================================================
-- IMPORT DỮ LIỆU BẢNG ___PROVINCE CHO DEVELOP
-- =====================================================
-- Chỉ import records mới với is_megre = 2
-- Tổng cộng: 34 tỉnh thành

USE urbox;

-- Tắt kiểm tra foreign key tạm thời
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

START TRANSACTION;

-- Xóa dữ liệu cũ nếu có (chỉ records với is_megre = 2)
DELETE FROM ___province WHERE is_megre = 2;

-- Import dữ liệu mới
EOF

# Export dữ liệu province
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "
SELECT CONCAT(
    'INSERT INTO ___province (id, mb_id, pti_id, parent_id, title, safe_title, position, is_city, area, ship_price, ship_constant, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_megre) VALUES (',
    id, ', ',
    IFNULL(mb_id, 'NULL'), ', ',
    IFNULL(pti_id, 'NULL'), ', ',
    IFNULL(parent_id, 'NULL'), ', ',
    '''', REPLACE(title, '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(safe_title, ''), '''', ''''''), ''', ',
    position, ', ',
    is_city, ', ',
    IFNULL(area, 'NULL'), ', ',
    IFNULL(ship_price, 'NULL'), ', ',
    IFNULL(ship_constant, 'NULL'), ', ',
    status, ', ',
    created_at, ', ',
    updated_at, ', ',
    '''', REPLACE(IFNULL(created_by, ''), '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(updated_by, ''), '''', ''''''), ''', ',
    IFNULL(deleted_at, 'NULL'), ', ',
    IFNULL(CONCAT('''', REPLACE(IFNULL(deleted_by, ''), '''', ''''''), ''''), 'NULL'), ', ',
    is_megre, ');'
)
FROM ___province 
WHERE is_megre = 2 
ORDER BY position;
" >> $PROVINCE_FILE

# Thêm footer
cat >> $PROVINCE_FILE << 'EOF'

COMMIT;

-- Bật lại kiểm tra foreign key
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- Kiểm tra kết quả
SELECT 'KIỂM TRA PROVINCE IMPORT' as info;
SELECT COUNT(*) as imported_provinces FROM ___province WHERE is_megre = 2;
SELECT 'Import province hoàn thành!' as status;
EOF

echo "✅ Export province thành công: $PROVINCE_FILE"

# =====================================================
# PHẦN 3: EXPORT BẢNG WARD
# =====================================================

echo ""
echo "📤 EXPORT BẢNG WARD..."

WARD_FILE="$EXPORT_DIR/ward_data_$DATE.sql"

# Tạo header cho file
cat > $WARD_FILE << 'EOF'
-- =====================================================
-- IMPORT DỮ LIỆU BẢNG WARD CHO DEVELOP
-- =====================================================
-- Chỉ import records mới với is_merge = 2
-- Tổng cộng: 3,321 xã phường

USE urbox;

-- Tắt kiểm tra foreign key tạm thời
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

START TRANSACTION;

-- Xóa dữ liệu cũ nếu có (chỉ records với is_merge = 2)
DELETE FROM ward WHERE is_merge = 2;

-- Import dữ liệu mới
EOF

# Export dữ liệu ward (chỉ lấy ward có province_id khớp với ___province mới)
echo "📋 Export ward data (chỉ lấy ward có province_id khớp với ___province mới)..."

mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -se "
SELECT CONCAT(
    'INSERT INTO ward (id, province_id, district_id, pti_id, prefix, title, title_full, status, created_at, updated_at, created_by, updated_by, deleted_at, deleted_by, is_merge) VALUES (',
    w.id, ', ',
    IFNULL(w.province_id, 'NULL'), ', ',
    IFNULL(w.district_id, 'NULL'), ', ',
    IFNULL(w.pti_id, 'NULL'), ', ',
    '''', REPLACE(IFNULL(w.prefix, ''), '''', ''''''), ''', ',
    '''', REPLACE(w.title, '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(w.title_full, ''), '''', ''''''), ''', ',
    w.status, ', ',
    w.created_at, ', ',
    w.updated_at, ', ',
    '''', REPLACE(IFNULL(w.created_by, ''), '''', ''''''), ''', ',
    '''', REPLACE(IFNULL(w.updated_by, ''), '''', ''''''), ''', ',
    IFNULL(w.deleted_at, 'NULL'), ', ',
    IFNULL(CONCAT('''', REPLACE(IFNULL(w.deleted_by, ''), '''', ''''''), ''''), 'NULL'), ', ',
    w.is_merge, ');'
)
FROM ward w
INNER JOIN ___province p ON w.province_id = p.id
WHERE w.is_merge = 2 AND p.is_megre = 2
ORDER BY w.province_id, w.id;
" >> $WARD_FILE

# Thêm footer
cat >> $WARD_FILE << 'EOF'

COMMIT;

-- Bật lại kiểm tra foreign key
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- Kiểm tra kết quả
SELECT 'KIỂM TRA WARD IMPORT' as info;
SELECT COUNT(*) as imported_wards FROM ward WHERE is_merge = 2;
SELECT 'Import ward hoàn thành!' as status;
EOF

echo "✅ Export ward thành công: $WARD_FILE"

# =====================================================
# PHẦN 4: TẠO FILE IMPORT TỔNG HỢP
# =====================================================

echo ""
echo "📦 TẠO FILE IMPORT TỔNG HỢP..."

COMBINED_FILE="$EXPORT_DIR/province_ward_complete_$DATE.sql"

cat > $COMBINED_FILE << 'EOF'
-- =====================================================
-- IMPORT TỔNG HỢP DỮ LIỆU PROVINCE VÀ WARD - DEVELOP
-- =====================================================
-- Tạo: $(date)
-- Database: urbox
-- Bao gồm: ___province (34), ward (3,321)

USE urbox;

-- Tắt kiểm tra foreign key tạm thời
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

START TRANSACTION;

EOF

# Thêm dữ liệu province
echo "-- ===== DỮ LIỆU PROVINCE =====" >> $COMBINED_FILE
cat $PROVINCE_FILE | grep -E "^(INSERT|DELETE)" >> $COMBINED_FILE

echo "" >> $COMBINED_FILE
echo "-- ===== DỮ LIỆU WARD =====" >> $COMBINED_FILE
cat $WARD_FILE | grep -E "^(INSERT|DELETE)" >> $COMBINED_FILE

# Thêm phần kiểm tra cuối
cat >> $COMBINED_FILE << 'EOF'

COMMIT;

-- Bật lại kiểm tra foreign key
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- KIỂM TRA TỔNG HỢP
SELECT 'KIỂM TRA KẾT QUẢ IMPORT' as info;

SELECT 
    'Province' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_megre = 2 THEN 1 END) as new_records,
    '34' as expected_records
FROM ___province
UNION ALL
SELECT 
    'Ward' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_merge = 2 THEN 1 END) as new_records,
    '3321' as expected_records
FROM ward;

-- Kiểm tra mapping province -> ward
SELECT 
    p.id as province_id,
    p.title as province_title,
    COUNT(w.id) as ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_merge = 2
WHERE p.is_megre = 2
GROUP BY p.id, p.title
ORDER BY p.position
LIMIT 10;

SELECT 'IMPORT HOÀN THÀNH!' as status;
EOF

echo "✅ Tạo file import tổng hợp thành công: $COMBINED_FILE"

# =====================================================
# PHẦN 5: THỐNG KÊ VÀ BÁO CÁO
# =====================================================

echo ""
echo "📊 THỐNG KÊ DỮ LIỆU EXPORT..."

echo "📈 Số lượng dữ liệu đã export:"
echo "   - Province: $PROVINCE_NEW records"
echo "   - Ward: $WARD_NEW records"

# Tính kích thước file
echo ""
echo "💾 Kích thước file export:"
ls -lh $EXPORT_DIR/*$DATE* | awk '{print "   - " $9 ": " $5}'

echo ""
echo "🎉 HOÀN THÀNH EXPORT!"
echo "📁 Các file đã tạo trong thư mục: $EXPORT_DIR"
echo "🚀 File chính để import: province_ward_complete_$DATE.sql"
echo ""
echo "📋 HƯỚNG DẪN IMPORT TRÊN DEVELOP:"
echo "   1. Upload file province_ward_complete_$DATE.sql lên server develop"
echo "   2. Backup dữ liệu cũ: mysqldump -u username -p urbox ___province ward > backup.sql"
echo "   3. Chạy import: mysql -u username -p urbox < province_ward_complete_$DATE.sql"
echo "   4. Kiểm tra kết quả trong script"
echo ""
echo "✅ Sẵn sàng cho việc import lên develop!"
