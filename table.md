CREATE TABLE `brand_store` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `pos_id` int DEFAULT '0' COMMENT 'POS ID',
  `brand_id` int DEFAULT '0' COMMENT 'Brand của mình',
  `brando_id` int DEFAULT '0' COMMENT 'Brand Office ID',
  `store_id` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '' COMMENT 'Store ID của bên pos gắn với 1 cửa hàng của thương hiệu',
  `city_id` int DEFAULT '0',
  `district_id` int DEFAULT '0',
  `ward_id` int DEFAULT '0',
  `street_id` int DEFAULT '0',
  `title` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `brand_title` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `title_city_old` varchar(70) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT '',
  `title_city` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `title_district` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `title_ward_old` varchar(70) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT '',
  `title_ward` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `title_street` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `number` varchar(70) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `address` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `address2` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `latitude` decimal(10,5) DEFAULT '0.00000',
  `longitude` decimal(10,5) DEFAULT '0.00000',
  `geo` text COLLATE utf8mb3_unicode_ci,
  `status` tinyint(1) DEFAULT '2',
  `created_at` int NOT NULL DEFAULT '0',
  `updated_at` int NOT NULL DEFAULT '0',
  `created_by` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'system' COMMENT 'Người tạo bản ghi, mặc định là system. Với các bản ghi tạo từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người tạo thì sẽ là email của người đó ví dụ <EMAIL>',
  `updated_by` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'Người cập nhật bản ghi, mặc định là system. Với các bản ghi cập nhật từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người cập nhật thì sẽ là email của người đó ví dụ <EMAIL>',
  `deleted_at` int DEFAULT '0',
  `deleted_by` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'Người xoá bản ghi, mặc định là system. Với các bản ghi xoá từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người xoá thì sẽ là email của người đó ví dụ <EMAIL>',
  `is_updated` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_brand_id` (`brand_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `brand_store_pos_id_index` (`pos_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19701 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `merge_ward` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pti_id` int DEFAULT NULL,
  `description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `new_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `ward` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `province_id` int DEFAULT '0',
  `district_id` int unsigned DEFAULT '0',
  `pti_id` int DEFAULT '0' COMMENT 'Mã phường của PTI',
  `prefix` varchar(20) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `title` varchar(50) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '',
  `title_full` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT '',
  `status` tinyint(1) DEFAULT '2',
  `created_at` int NOT NULL DEFAULT '0',
  `updated_at` int NOT NULL DEFAULT '0',
  `created_by` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'system' COMMENT 'Người tạo bản ghi, mặc định là system. Với các bản ghi tạo từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người tạo thì sẽ là email của người đó ví dụ <EMAIL>',
  `updated_by` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'Người cập nhật bản ghi, mặc định là system. Với các bản ghi cập nhật từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người cập nhật thì sẽ là email của người đó ví dụ <EMAIL>',
  `deleted_at` int DEFAULT '0',
  `deleted_by` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'Người xoá bản ghi, mặc định là system. Với các bản ghi xoá từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người xoá thì sẽ là email của người đó ví dụ <EMAIL>',
  `is_new` int DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `_province_id` (`district_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13136 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `___province` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mb_id` int DEFAULT NULL,
  `pti_id` int DEFAULT '0' COMMENT 'Mã vùng của pti',
  `parent_id` int DEFAULT '0',
  `title` varchar(255) NOT NULL,
  `safe_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT '',
  `position` tinyint NOT NULL DEFAULT '99',
  `is_city` tinyint(1) DEFAULT '0' COMMENT '0: Tinh | 1: Thanh pho',
  `area` tinyint(1) DEFAULT '0' COMMENT '1: Miền Bắc, 2: Miền Trung, 3: miền nam',
  `ship_price` int DEFAULT '0',
  `ship_constant` float DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '2',
  `created_at` int NOT NULL DEFAULT '0',
  `updated_at` int NOT NULL DEFAULT '0',
  `created_by` varchar(255) NOT NULL DEFAULT 'system' COMMENT 'Người tạo bản ghi, mặc định là system. Với các bản ghi tạo từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người tạo thì sẽ là email của người đó ví dụ <EMAIL>',
  `updated_by` varchar(255) DEFAULT NULL COMMENT 'Người cập nhật bản ghi, mặc định là system. Với các bản ghi cập nhật từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người cập nhật thì sẽ là email của người đó ví dụ <EMAIL>',
  `deleted_at` int DEFAULT '0',
  `deleted_by` varchar(255) DEFAULT NULL COMMENT 'Người xoá bản ghi, mặc định là system. Với các bản ghi xoá từ hệ thống sẽ có dạng system:<tên hệ thống>, với các bản ghi do người xoá thì sẽ là email của người đó ví dụ <EMAIL>',
  `is_new` int DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `position` (`position`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb3;

CREATE TABLE `ward_geometry` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pti_id` int DEFAULT NULL,
  `data` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;